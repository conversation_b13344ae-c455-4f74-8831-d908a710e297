import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)

df = pd.read_csv("./raw_data/20250320_gran_base_signature_v2.csv", delimiter = ";")

# Count and percentage of missing values
missing_values = pd.DataFrame({
    'Missing Count': df.isna().sum(),
    'Missing Percentage': (df.isna().sum() / len(df) * 100).round(2)
})
missing_values.head()

#imputing all columns with data type = 0
for col in df.select_dtypes(include="object").columns:
    df[col] = df[col].fillna("DESCONOCIDO")

# Convert to datetime format
df['fec_hora_ocurrencia'] = pd.to_datetime(df['fec_hora_ocurrencia'])

unique_years = df['fec_hora_ocurrencia'].dt.year.unique()
unique_months = df['fec_hora_ocurrencia'].dt.to_period('M').unique()

print(f"\nUnique years: {sorted(unique_years)}")
print(f"Number of unique years: {len(unique_years)}")

print(f"\nUnique year-months: {sorted(unique_months)}")
print(f"Number of unique months: {len(unique_months)}")

df.head()

df.clasificacion_de_riesgo.value_counts()

df.descrpicion_riesgo.value_counts()


df.especialidad_diagnostico.value_counts()

df.des_cobertura.value_counts()

